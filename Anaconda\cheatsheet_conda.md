## CHEAT SHEET - CONDA

Conda es un sistema de gestión de paquetes y entornos que se puede ejecutar en Windows, macOS y Linux. Conda a través de `channels` o canales (ubicaciones de repositorios en los que busca paquetes) nos permite instalar, ejecutar y actualizar paquetes y sus dependencias. Del mismo modo, nos permite guardar, cargar y cambiar entre entornos en su computadora local fácilmente.

Para tenerlo claro debemos saber diferenciar Conda de Miniconda, este último combina Conda con Python y una pequeña cantidad de paquetes centrales; por otro lado, al instalar Anaconda viene incluido Miniconda, así como una gran cantidad de los paquetes de Python más utilizados.

__Nota:__ Existe también la opción de trabajar con [Anaconda Navigator](https://docs.anaconda.com/navigator/getting-started/), una interfaz gráfica de usuario que le permite usar conda en una interfaz similar a la web sin tener que ingresar comandos manuales.



<img src='https://bioconda.github.io/_images/tutorial-conda-miniconda-anaconda.jpg' alt='conda' width="420"/>

_Fuente: https://bioconda.github.io/tutorials/index.html_

### Descarga de Anaconda - Miniconda 

* Para obtener Anaconda: [Individual Edition](https://www.anaconda.com/products/individual)
* Para contar con Miniconda ingresar [aquí](https://conda.io/en/latest/miniconda.html)

Una vez abierto nuestro terminal o línea de comandos (*Anaconda Prompt* en Windows) desde donde podemos ejecutar diversos comandos. 

___Nota:___ _Para obtener la documentación con detalle de cualquier comando, adicionamos `--help`  al comando. Por ejemplo: `conda install --help`_


### Pasos Iniciales

* Verificar si tenemos instalado __Conda__, revisando el número de versión: `conda info`
* Actualizar __Conda__ a la versión actual: `conda update -n base conda`
* Actualizar todos los paquetes de la última versión de Anaconda: `conda update anaconda`
* Visualizar nuestros canales de repositorios: `conda config --show channels`
* Incorporar canales como el __conda-forge__: `conda config --add channels conda-forge` 

### Trabajando con Entornos Conda

* Crear un nuevo entorno nombrado `"NOMBRE_ENV"` con una versión específica de Python: `conda create --name NOMBRE_ENV python=3.7`
* Crear un entorno incluyendo Paquetes `"PKG"` específicos incluyendo versiones: `conda create --name NOMBRE_ENV python=3.7 "PKG1>7.6", PKG2`
* Para especificar la locación de un entorno Conda dentro de un directorio de trabajo[^1]: `conda create --prefix ./env PKG1 PKG2 python=3.8`
  * Dentro de nuestro directorio de trabajo podemos activar el entorno: `conda activate ./env` 
* Para conocer versiones de paquetes: `conda search PKG`
* Para conocer versiones específicas en un canal determinado: `conda search "PKG1>1.2" -c <channel>`
* Otra opción que podemos usar para conocer versiones de paquetes: `conda search <channel>::PKG`
* Activando un entorno Conda nombrado: `conda activate NOMBRE_ENV`
* Desactivando el actual entorno[^2]: `conda deactivate`
* Listar todos los paquetes y versiones del entorno activo: `conda list`
  * Para listar los paquetes de un entorno nombrado: `conda list --name NOMBRE_ENV`
* listar todas las revisiones hechas en el entorno activo: `conda list --revisions`
  * Las revisiones de un entorno en particular: `conda list --name NOMBRE_ENV --revisions`
* Restablecer un entorno a una revisión previa: `conda install --name NOMBRE_ENV --revision NUMERO_REVISION`
* Borrar un entorno completo: `conda remove --name NOMBRE_ENV --all`

### Instalación de Paquetes

* Instalar un paquete  desde un canal específico: `conda install -n NOMBRE_ENV conda-forge::PKG`
  * Otra manera de indicar una instalación en un canal es: `conda install -c conda-forge -n NOMBRE_ENV PKG`
  * Para el caso de un entorno dentro de un subdirectorio `env` usamos: `conda install -c conda-forge -p ./env NOMBRE_PKG`
* Instalar un paquete con número de versión específico (por ejemplo 3.1.4): `conda install -n NOMBRE_ENV PKG==3.1.4`
  * Para instalar un paquete que puede contener un rango de versiones empleamos: `conda install -n NOMBRE_ENV PKG=3.1`
  * Para instalar cualquiera (OR) de dos paquetes: `conda install -n NOMBRE_ENV "PKG=3.1.4|3.1.6"`
  * Para instalar dos versiones específicas: `conda install -n NOMBRE_ENV "PKG>=3.1, < 3.3"` 
* Cuando requerimos instalar un paquete que no está en ningunos de los canales de Conda, debemos instalar PIP empleando: `conda install -n NOMBRE_ENV pip`
  * Luego de activar nuestro entorno con `conda activate NOMBRE_ENV`, vamos a instalar un paquete usando PIP[^3]: `python -m pip install PKG`

### Compartiendo Entornos

* Hacer una copia exacta de un entorno: `conda create --clone NOMBRE_ENV --name NEW_ENV`
* Exportar un entorno a un archivo YAML[^4], que puede ser leído con Windows, macOS y Linux: `conda env export --name NOMBRE_ENV > nombre_file.yml`
  * Si estamos dentro de un entorno en un subdirectorio: `conda env export --prefix ./env > nombre_file.yml`
* Crear un entorno desde un archivo YAML: `conda env create --file nombre_file.yml`
  * Crear un entorno en un subdirectorio: `conda env create --prefix ./env --file nombre_file.yml`
* Crear un entorno desde un archivo nombrado `environment.yml` en el directorio actual de trabajo[^5]: `conda env create`
* Exportando un entorno con las versiones de paquetes para un Sistema Operativo: `conda list --explicit > pkgs.txt`
* Crear un entorno basado con las versiones de paquetes definidos: `conda create --name NOMBRE_ENV --file pkgs.txt`
* Actualizar un entorno configurado en un archivo YAML[^6]: `conda env update -n NOMBRE_ENV -f nombre_file.yml --prune`
  * Para el caso de un entorno en un subdirectorio: `conda env update --prefix ./env -f nombre_file.yml  --prune`
* Para reconstruir un entorno conda desde cero a partir de un archivo YAML, eliminando cualquier directorio del entorno existente: `conda env create -n NOMBRE_ENV -f nombre_file.yml --force`

### Adicional

* Para detallar información sobre la versión de un paquete: `conda search PKG --info`
* Eliminar los archivos en caché no utilizados, incluidos los paquetes no utilizados: `conda clean --all`
* Remover un paquete desde un entorno: `conda uninstall PKG --name NOMBRE_ENV`
* Actualizar todos los paquetes dentro de un entorno: `conda update --all --name NOMBRE_ENV`
* Ejecutar la mayoría de los comandos sin necesidad de un mensaje de usuario. Útil para scripts: `conda install --yes PKG1 PKG2`
* Examinar la configuración y los servicios de configuración de Conda: `conda config --show` - `conda config --show-sources`
* Para que Jupyter logre reconocer un entorno Conda, es necesario considerar entre las depedencias a `ipykernel` y también `ipython`, con la finalidad de crear un kernel específico para el entorno: `python -m ipykernel install --user --name NOMBRE_ENV`


#### Referencias:

+  Documentación en línea de Conda: <https://conda.io>
+  Conda Cheat Sheet: <https://docs.conda.io/projects/conda/en/latest/user-guide/cheatsheet.html>
+  Documentación de Anaconda: <https://docs.anaconda.com/anaconda/>
+  Lecciones de Carpentry Incubator: <https://carpentries-incubator.github.io/introduction-to-conda-for-data-scientists/index.html>




[^1]: Se emplea cuando contamos con un proyecto dentro de un directorio, esto nos permite contar con un entorno específico en un subdirectorio llamado `env`.
[^2]: Se recomida emplear `conda activate` para retornar al entorno "base". Se debe evitar usar `conda deactivate` desde su entorno "base".
[^3]: No debemos emplear `pip`con el argumento `--user`.
[^4]: Si no especificamos ninguna ruta, para Windows el archivo se almacena dentro de `C:\Users\<USER>\`
[^5]: Si empleamos `conda env create` sin usar la opción `--file`, **conda** espera un archivo llamado `environment.yml`en el directorio de trabajo actual, arrojando un error si no lo encuentra.
[^6]: Tenga en cuenta que la opción `--prune` le dice a __Conda__ que elimine cualquier dependencia que ya no sea necesaria del entorno.




## HOJA DE REFERENCIA SOBRE GIT

### ¿ Qué es un Sistema de Control de Versiones?

Los sistemas de control de versiones (CVS por sus siglás en inglés) nos permiten gestionar los cambios de nuestros proyectos, los mismos que pueden ser el desarrollo de un software, conjunto de datos, libros, artículos, entre otros. Es una herramienta que realiza un seguimiento de los cambios que realizamos a nuestro proyecto por nosotros, creando efectivamente diferentes versiones de nuestros archivos. Nos permite decidir qué cambios se realizarán en la próxima versión (cada registro de estos cambios se denomina `commit`) y mantiene metadatos útiles sobre ellos. El historial completo de confirmaciones para un proyecto en particular y sus metadatos forman un __repositorio__. Los repositorios se pueden mantener sincronizados en diferentes computadoras, lo que facilita la colaboración entre diferentes personas.

Entre los CVS más conocidos tenemos a [git](https://git-scm.com/), aunque también existen otros como [Subversion](http://subversion.apache.org/), [Mercurial](https://www.mercurial-scm.org/), [Fossil](https://fossil-scm.org/home/<USER>/trunk/www/index.wiki), etc. Nosotros nos vamos a concentrar en conocer git.

<center><img src=https://git-scm.com/images/<EMAIL>></center>

### Concepto Principal de Git - Los tres estados

Una vez que tienes un repositorio local inicializado con Git, se considera que existen tres estados principales en los que se pueden encontrar tus archivos: 

1. Confirmado (commited): Significa que los datos están almacenados de manera segura en tu directorio de trabajo o tu base de datos local.
2. Modificado (modified): Significa que has modificado el archivo pero todavía no lo has confirmado a tu base de datos. 
3. Preparado (staged): Significa que has marcado un archivo modificado en su versión actual para que vaya en tu confirmación.

En un proyecto Git existen tres secciones principales: el directorio de trabajo (working directory), y el área de preparación (staging area) y el directorio de git (repository).

![](img/Screenshot_1-1.webp)

Fuente: <https://www.pragma.com.co/academia/lecciones/que-es-git-y-como-funciona>

### Descarga de Git y Clientes GUI

* Para contar con git para todas las sistemas operativos nos vamos a: <https://git-scm.com/>

* Para usuarios de Windows lo pueden hacer desde: <https://gitforwindows.org/>

* Si queremos contar con una interfáz gráfica (GIU) para automatizar algunos procesos, pero sobre todo para una mejor gestión de nuestros proyectos, existen las siguientes opciones que recomiendo:
  
  * GitKraken: <https://www.gitkraken.com/>
  * Sourcetree: <https://www.sourcetreeapp.com/>
  * GitHub Desktop: <https://desktop.github.com/>

* En la actualidad los entornos de desarrollo integrado (IDE por sus siglas en inglés) que conozco como  [Visual Studio Code](https://code.visualstudio.com/), [Pycharm](https://www.jetbrains.com/es-es/pycharm/), [RStudio](https://www.rstudio.com/products/rstudio/) y [Atom](https://atom.io/), tienen la capacidad de manejar el control de versiones, siempre que tengamos instalado en nuestro equipo a __git__.
  
  ### Configuración Inicial:

Desde la línea de comandos (terminal) realizar esto antes que cualquier otra acción.

* Configuración global de tu nombre para realizar los "commit": `git config --global user.name "Carlos Carbajal"`
* Configuración global de tu correo asociado a los "commit": `git config --global user.email "<EMAIL>"`
* Para comprobar tu configuración: `git config --list`
* Para habilitar tener resultados coloreados en el terminal podemos usar: `git config --global color.ui auto`

### Crear repositorio Git:

* Estando dentro de nuestro directorio de trabajo existente: `git init`
* Podemos clonar un repositorio existente[^1]: `git clone [url]`
  * Ejemplo de URL[^2]: `https://github.com/Nombre_Usuario/Nombre_Repositorio.git`
* Podemos adicionar un repositorio remoto: `git remote add origin [url]`

### Ejecuciones más comunes a nuestro repositorio

* Iniciar el seguimiento de archivos nuevos, enviándolos al área de espera: `git add <archivos_nuevos>`
  * Podemos realizar el seguimiento de todos nuestros archivos nuevos: `git add --all`
* Revisar y enumerar todos los archivos nuevos o modificados que deben ser confirmados: `git status`
* Mostrar las diferencias de archivos que no se enviaron al área de espera: `git diff`
  * Mostrar las diferencias del archivo entre el área de espera y la última versión del archivo[^3]: `git diff --staged`
* Para confirmar los cambios realizados: `git commit -m "<Mensaje de confirmación>"`
* Listar los últimos "commit" sobre tu repositorio local: `git log`
  * Existen opciones para mejorar el formato de salida[^4], como por ejemplo, en caso queremos que nos imprima cada confirmación en una sola línea usamos: `git log --pretty=oneline`
* Eliminar un archivo: `git rm <archivo>`
  * En caso deseas mantener el archivo en tu directorio de trabajo pero eliminarlo del área de espera o preparación, es decir que Git no lo rastree: `git rm --cached <archivo>`

### Ejecuciones menos comunes a nuestro repositorio

* Para descartar los cambios realizados de nuestro archivo[^5], es decir volver al estado que estaba en la última confirmación (o como estaba cuando fue clonado o el estado inicial en tu directorio de trabajo): `git checkout -- <nombre_archivo>`

* En el caso que necesites sacar del área de espera un archivo que fue ingresado por error: `git reset HEAD <nombre_archivo>`

![](img/reset_checkout.jpg)

_Fuente: <https://nianbemo.gitbook.io/cutepixelab/apuntes/git-1>_

* Si luego de confirmar te olvidaste de preparar los cambios de un arhivo que se incluye en dicha confirmación, luego de usar `git add <archivo olvidado>`, simplemente usas: `git commit --ammed`

### Trabajar con Remotos

Los repositorios remotos nos permiten tener una versión de nuestro proyecto alojados en la nube. Para ello existen opciones como [GitHub](https://github.com/), [GitLab](https://about.gitlab.com/) y [Bitbucket](https://bitbucket.org/).

![](img/serv_remotos.png)

* Para comprobar la URL que Git asocia al nombre de tu repositorio remoto, estando dentro de tu carpeta local puedes usar: `git remote -v`
* Para obtener datos de tus proyectos remotos puedes ejecutar: `git fetch [nombre remoto]`
* Para consultar si tenemos alguna rama[^6] creada a nuestro proyecto: `git branch`
* Para crear una rama ("branch") usamos: `git branch <nombre_rama>`
* Para cambiarnos y trabajar en la nueva rama creada: `git checkout <nombre_rama>`
* Cuando necesitamos unir los cambios realizados en nuestra rama creada a la rama principal ("master"), primero debemos pasarnos a la rama principal empleando: `git checkout master`; luego desde "master" empleamos: `git merge <nombre_rama>`

![](img/git%20branches.jpg)

_Fuente: https://nianbemo.gitbook.io/cutepixelab/apuntes/git-1/3.-branching_

* Si necesitamos que los cambios realizados sean subidos a la rama "master" de nuestro repositorio remoto usamos: `git push origin master`
* Para contar con los cambios hechos en el repositorio remoto hacia nuestro repositorio local[^7]: `git fetch`
* Para realizar un `fetch` y un `merge` de manera automática del repositorio remoto en nuestra rama actual[^8] usamos: `git pull origin master`

![](img/git-pull.png)

_Fuente: https://www.maixuanviet.com/git-pull-pull-request.vietmx_

### Participación en Proyectos

Para realizar esto debemos conocer dos procesos relacionados.

#### - Bifurcación (fork) de proyectos

Si deseamos tener una participón en un proyecto existente en un repositorio remoto, tenemos la opción de bifurcarlo (hacer un "fork"). Esto consiste en crear una copia completa del repositorio totalmente bajo tu control, es decir que se almacenará en tu cuenta y podrás escribir en él sin limitaciones.

#### - Generar un Pull Request

Usando "Pull Request" estamos anunciando un cambio realizado por nosotros en una rama. Una vez abierto el "Pull Request", se nos permite abrir una discusión para la revisión del proyecto, donde el propietario y usted pueden comunicarse acerca de los cambios y, en última instancia, el propitario original puede aceptarlos e integrarlos en el proyecto original cuando lo considere adecuado.

En los servicios como GitHub tienen por lo general el siguiente flujo de trabajo:

    1. Realizar un fork a un proyecto que nos interesa
    2. Clonar nuestro fork en nuestro equipo
    3. Realizar nuestros cambios
    4. Realizar un commit de los cambios
    5. Enviar nuestros cambios confirmados a nuestro fork (Push)
    6. Realizar un Pull request

![](img/github1.png)

_Fuente: https://www.dataschool.io/simple-guide-to-forks-in-github-and-git/_

### Adicionales:

* Podemos configurar un alias (entendido como "acceso rápido") con la finalidad de reducir la escritura de comandos más comunes en Git. Se muestran algunos ejemplos,los cuales permitiran usar `ci` en lugar de `commit`y `st` en lugar de `status` respectivamente.
  * `git config --global alias.ci commit` 
  * `git config --global alias.st status`
* Para cambiar de nombre a nuestros archivos: `git mv <nombre_archivo_actual> <nombre_archivo_nuevo>`
* Podemos ignorar archivos creando un archivo `.gitignore`, para luego con un editor (como Vim o Nano) indicar un patrón que debe tener un archivo para ser ignorado. Por ejemplo con `*.[ea]`, le indicamos a Git que ignore cualquier archivo que termine en ".e" o ".a".   

### Referencias:

* Book Git: https://git-scm.com/book/es/v2
* Atlassian-Bitbucket Tutoriales: https://www.atlassian.com/es/git/tutorials/learn-git-with-bitbucket-cloud
* Documentación GitHub: https://docs.github.com/es
* Lección Data Carpentry: https://datacarpentry.org/rr-version-control/
* Software-Carpentry: https://swcarpentry.github.io/git-novice/ 
* Taller de Git: https://aulasoftwarelibre.github.io/taller-de-git/
* Cute Pixel Lab: https://nianbemo.gitbook.io/cutepixelab/apuntes/git-1
* Vietmx Blog: https://www.maixuanviet.com/git-pull-pull-request.vietmx

[^1]: La clonación se puede realizar a una carpeta específica, por lo tanto al final podemos hacer la indicación siguiendo este esquema: `git clone <repositorio><directorio>`
[^2]: Tener en cuenta que los protocolos de URL para Git son: SSH (Secure Shell), GIT y el HTTP.
[^3]: También podemos emplear `git diff --cached`, para tener los mismos resultados.
[^4]: La variedad de opciones pueden ser revisadas [aquí](https://git-scm.com/book/es/v2/Fundamentos-de-Git-Ver-el-Historial-de-Confirmaciones#rpretty_format)
[^5]: Considerar que el uso de este comando implica que cualquier cambio que hayas hecho a ese archivo desaparecerá, acabas de sobreescribirlo con otro archivo.
[^6]: En Git se puede realizar el "branching", que tiene la finalidad de crear ramas para trabajar fuera de la línea principal ("master"), con la finalidad de no alterarlo.
[^7]: Con `git fetch`solo hacemos la descarga de datos a nuestro local, por lo que debemos realizar el `merge` manualmente.
[^8]: En caso solo trabajamos con la rama principal empleamos "master", en caso estemos sobre una rama creada, debemos indicarlo `git pull <remoto> <nombre_rama>`.
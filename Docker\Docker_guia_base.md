# Guía básica de Docker

![](https://milpuntos.es/wp-content/uploads/docker.png)

### Qué es Docker?

Docker es una plataforma creada con el fin de desarrollar, implementar y ejecutar **aplicaciones dentro de contenedores.** Docker empaqueta diversos tipos de desarrollo en “contenedores” que incluyen en ellos todo lo necesario (dependencias) para que nuestras aplicaciones se logren ejecutar.  En **Docker** lo que se hace es usar las funcionalidades del Kernel para encapsular un sistema, de esta forma el proyecto que corre dentro de el no tendrá conocimiento que está en un contenedor. Los contenedores se encuentran aislados entre sí y se comportaran como máquinas independientes.

### Proceso de creación de un contenedor en Docker

Dentro del contenedor se definen una serie de instrucciones (**dockerfile**) que permite crear una imagen (**docker image**) a través del cual se podrá arrancar a dicho contenedor (**docker container**).

![](https://profile.es/wp-content/media/image-1-1024x266.png)

- **Dockerfile:** es el documento de texto sobre el que podemos agrupar una serie de comandos con el fin que se ejecuten todos a la vez evitando así tener que ejecutarlos uno a uno manualmente, resultando que el proceso de creación de una imagen de Docker sea mucho más rápido y eficiente.

- **Docker image:** una imagen de Docker, contiene las librerías, junto al código de la aplicación que contiene todo lo necesario para ejecutar nuestra aplicación.

- **Container:** es una imagen de Docker cuando empieza a funcionar, es decir, cuando cobra vida.

## Primeros Pasos

Para realizar la instalación en Linux se recomienda seguir los pasos descritos en este caso para [linux_ubuntu](https://docs.docker.com/engine/install/ubuntu/).

*Nota: Para ejecutar Docker sin privilegios de raíz, consulte Ejecutar el demonio de Docker como usuario no raíz (modo sin raíz).*

Para crear el grupo docker y agregar su usuario:  

1. Cree el grupo de docker.  
   
   ```
    sudo groupadd docker
   ```

2. Agregue su usuario al grupo docker
   
   ```
    sudo usermod -aG docker $USER
   ```

3. Cierre la sesión y vuelva a iniciarla para que se vuelva a evaluar la pertenencia a su grupo. En Linux, también puede ejecutar el siguiente comando para activar los cambios en los grupos:
   
   ```
    newgrp docker 
   ```

## Principales Comandos:

### General - Images

| Comando                         | Descripción                                                                                                                        |
| ------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| docker --version                | verificando la versión de docker instalado                                                                                         |
| docker --help                   | muestra todos los comando disponibles en docker                                                                                    |
| docker info                     | muestra la información del sistema sobre el que se ejecuta docker, la versión del kernel, el número de contenedores y las imágenes |
| docker images                   | visualiza todas la imágenes disponibles                                                                                            |
| docker build -t IMAGE DIRECTORY | Crea una imagen desde un Dockerfile                                                                                                |
| docker tag IMAGE NEW IMAGE      | Etiquetar (Tag) una imagen.                                                                                                        |
| docker pull IMAGE[:TAG]         | Descarga de una imagen (existen en Docker Hub)                                                                                     |
| docker push IMAGE               | Subir una imagen a un repositorio                                                                                                  |
| docker rmi [IMAGE ID]           | Eliminar una imagen                                                                                                                |

### Contenedores

| Comando                                             | Descripción                                                      |
| --------------------------------------------------- | ---------------------------------------------------------------- |
| docker run [IMAGE]                                  | Inicia un nuevo contenedor desde una imagen                      |
| docker run --name [CONTAINER IMAGE]                 | Inicia y asigna un nombre de contenedor desde una imagen         |
| docker run -it [IMAGE]                              | Inicia un contenedor de manera interactiva                       |
| docker run -p [HOSTPORT: CONTAINER PORT] [IMAGE]    | Inicia y especifica los puertos al contenedor desde una imagen   |
| docker run -p [HOSTPORT: CONTAINER PORT] -d [IMAGE] | Inicia pero en modo "detach" es decir en segundo plano           |
| docker stop [CONTAINER ID]                          | Detiene la ejecución de un contenedor                            |
| docker start [CONTAINER ID]                         | Iniciar la ejecucución de un contenedor detenido                 |
| docker ps                                           | Muestra la lista de contenedores corriendo                       |
| docker ps -a                                        | Muestra la lista de todos los contenedores                       |
| docker ps -aq                                       | Muestra los ID de todos los contenedores                         |
| docker ps --filter label=[NAME]                     | Muestra los contenedores con una etiqueta específica.            |
| docker rm [CONTAINER]                               | Elimina un contenedor (se debe detener primero)                  |
| docker rm -f [CONTAINER]                            | Elimina un conteneder que está corriendo                         |
| docker rm $(docker ps -aq)                          | Elimina toda la lista de contenedores existentes                 |
| docker rm $(docker ps --filter status=exited -q)    | Elimina todos los contenedores detenidos                         |
| docker run --name [NEW NAME] [IMAGE]                | Inicia un contenedor asignándole un nuevo nombre                 |
| docker rename [OLD NAME NEW NAME]                   | Renombrar un contenedor                                          |
| docker logs [CONTAINER]                             | Muestra información del contenedor                               |
| docker stats                                        | Muestra las estadísticas de los contenedores que están corriendo |
| docker top [CONTAINER]                              | Muestra los procesos del cotenedor                               |

### Otros - Redes

| Comando                                         | Descripción                                                                                |
| ----------------------------------------------- | ------------------------------------------------------------------------------------------ |
| docker exec -it [CONTAINER] [PROCESS]           | EJecuta de manera interactiva otro proceso (*bash* por ejemplo) en un contenedor iniciado. |
| docker logs -f [CONTAINER]                      | Muestra el detalle de un contenedor ejecutándose                                           |
| docker port [CONTAINER]                         | Muestra los puertos empleados en un contenedor                                             |
| docker network create [NAME NETWORK]            | Creación de una red local                                                                  |
| docker run -d --net [NETWORK] [IMAGE]           | Adjuntar un contenedor a una red al ejecutarlo                                             |
| docker network connect [NETWORK] [CONTAINER]    | Conectar un contenedor iniciado desde una red                                              |
| docker network disconnect [NETWORK] [CONTAINER] | Desconectar un contenedor desde una red                                                    |
